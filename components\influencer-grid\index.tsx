"use client"

import { useState, useEffect, use<PERSON>emo } from "react"
import { <PERSON><PERSON>, Di<PERSON>Trigger, DialogContent } from "@/components/ui/dialog"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { useToast } from "@/components/ui/use-toast"
import { toast } from "sonner"
import axios from "axios"

// ===== GRAPHQL IMPORTS =====
import { ApolloProvider, useApolloClient } from '@apollo/client';
import { useAuth } from "@/hooks/use-auth-v2";
import useInfluencersGraphQL from "@/hooks/use-influencers-graphql";
import { useGlobalLoader } from "@/components/ui/loader";
import { useQuery, gql } from '@apollo/client';
import { useUser } from '@clerk/nextjs';

// ===== QUERY GRAPHQL PARA PROPOSTAS =====
const GET_PROPOSAL_INFLUENCERS = gql`
  query GetProposalInfluencers($influencerIds: [ID!]!, $userId: ID!, $proposalId: ID) {
    influencersByIds(ids: $influencerIds, userId: $userId, proposalId: $proposalId) {
      influencers {
        id
        name
        avatar
        totalFollowers
        engagementRate
        rating
        isVerified
        isAvailable
        status
        category
        country
        state
        city
        age
        gender
        email
        phone
        whatsapp
        
        # Redes sociais com TODOS os campos de views
        instagramUsername
        instagramFollowers
        instagramEngagementRate
        instagramAvgViews
        instagramStoriesViews
        instagramReelsViews
        
        tiktokUsername
        tiktokFollowers
        tiktokEngagementRate
        tiktokAvgViews
        tiktokVideoViews
        
        youtubeUsername
        youtubeFollowers
        youtubeEngagementRate
        youtubeAvgViews
        youtubeShortsViews
        youtubeLongFormViews
        
        facebookUsername
        facebookFollowers
        facebookEngagementRate
        facebookAvgViews
        facebookViews
        facebookReelsViews
        facebookStoriesViews
        
        twitchUsername
        twitchFollowers
        twitchEngagementRate
        twitchViews
        
        kwaiUsername
        kwaiFollowers
        kwaiEngagementRate
        kwaiViews
        
        promotesTraders
        responsibleName
        agencyName
        responsibleCapturer
        
        # Rede social principal
        mainNetwork
        
        # Pricing atual
        currentPricing {
          id
          services {
            instagram {
              story { price currency }
              reel { price currency }
            }
            tiktok {
              video { price currency }
            }
            youtube {
              insertion { price currency }
              dedicated { price currency }
              shorts { price currency }
            }
            facebook {
              post { price currency }
              story { price currency }
            }
            twitch {
              stream { price currency }
            }
            kwai {
              video { price currency }
            }
          }
          isActive
          validFrom
          validUntil
        }

        # Demographics atuais
        currentDemographics {
          id
          platform
          audienceGender {
            male
            female
            other
          }
          audienceLocations {
            country
            percentage
          }
          audienceCities {
            city
            percentage
          }
          audienceAgeRange {
            range
            percentage
          }
          isActive
          source
        }

        # Orçamentos por plataforma
        budgets {
          instagram {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          tiktok {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          youtube {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          facebook {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          twitch {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          kwai {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          personalizado {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
        }

        createdAt
        updatedAt
      }
      foundIds
      notFoundIds
      totalFound
      totalRequested
      processingTimeMs
      hasPartialFailure
      errors
    }
  }
`;


// Importar componentes refatorados
// Modal removido conforme solicitado
import { AnimatedStats } from "./animated-stats"
import { GridView } from "./grid-view"
import { DataTable } from "@/components/ui/data-table"
import { ColumnDef } from "@tanstack/react-table"
import { InfluencerAvatar } from "@/components/ui/influencer-avatar"
import { ZeroLCPInfluencerAvatar } from "@/components/ui/zero-lcp-avatar"
import { Badge } from "@/components/ui/badge"
import { SocialIcon } from "@/components/ui/social-icons"
import { Square, CheckSquare } from "lucide-react"
import { Influencer, Brand, globalBrands, gradients } from "./types"
import { getBrandById, getInfluencerBrands } from "./utils"
import { useTranslations } from "@/hooks/use-translations"
import { openDeleteDialog, handleDeleteInfluencer, toggleInfluencerSelection, openBulkDeleteDialog, handleBulkDelete, handleBulkDuplicate } from "./controllers"


// Importar outros componentes da aplicação
import { AddInfluencerForm } from "@/components/add-influencer-form"
import useInfiniteScroll from "@/hooks/use-infinite-scroll"

// Importar CSS para esconder a barra de rolagem mas manter a funcionalidade
import "../scrollbar-hide.css"

// Interface para a categoria
interface Category {
  id: string;
  name: string;
  slug: string;
  color?: string;
}

// Função para formatar números em K e MI
const formatarNumero = (numero: string | number): string => {
  const num = typeof numero === 'string' ? parseInt(numero.replace(/[^0-9]/g, '')) : numero;
  
  if (isNaN(num)) return '0';
  
  if (num >= 1000000) {
    return (num / 1000000).toFixed(num % 1000000 === 0 ? 0 : 1).replace('.0', '') + 'MI';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(num % 1000 === 0 ? 0 : 1).replace('.0', '') + 'K';
  } else {
    return num.toString();
  }
};

// Função para formatar localização
const formatLocationDisplay = (influencer: Influencer) => {
  if (influencer.location && influencer.location.trim() !== "") {
    return influencer.location;
  }
  
  // Type-safe check for GraphQL fields that might not exist in the base interface
  const infAny = influencer as any;
  const validCity = infAny.city && infAny.city !== "Não informado" && infAny.city.trim() !== "" ? infAny.city : null;
  const validState = infAny.state && infAny.state !== "Não informado" && infAny.state.trim() !== "" ? infAny.state : null;
  const validCountry = infAny.country && infAny.country !== "Não informado" && infAny.country.trim() !== "" ? infAny.country : null;
  
  if (validCity && validState) {
    return `${validCity}, ${validState}`;
  } else if (validCity && validCountry) {
    return `${validCity}, ${validCountry}`;
  } else if (validState && validCountry) {
    return `${validState}, ${validCountry}`;
  } else if (validCountry) {
    return validCountry;
  } else if (validCity) {
    return validCity;
  } else if (validState) {
    return validState;
  }
  return "Localização não informada";
};

interface InfluencerGridProps {
  onRefresh?: number;
  searchTerm?: string;
  selectedLocation?: string;
  minFollowers?: number;
  maxFollowers?: number;
  minRating?: number;
  verifiedOnly?: boolean;
  availableOnly?: boolean;
  onSelectInfluencer?: (influencer: Influencer) => void;
  selectedBrands?: string[];
  selectedProposal?: any;
  selectedInfluencerId?: string | null;
  selectedInfluencer?: Influencer | null;
  // 🔗 NOVO: Props para lista compartilhada
  sharedInfluencerIds?: string[];
  isSharedList?: boolean;
}

export default function InfluencerGrid({
  onRefresh,
  searchTerm: externalSearchTerm,
  selectedLocation: externalLocation,
  minFollowers: externalMinFollowers,
  maxFollowers: externalMaxFollowers,
  minRating: externalMinRating,
  verifiedOnly: externalVerifiedOnly,
  availableOnly: externalAvailableOnly,
  onSelectInfluencer,
  selectedBrands: externalSelectedBrands = [],
  selectedProposal,
  selectedInfluencerId,
  selectedInfluencer: externalSelectedInfluencer,
  // 🔗 NOVO: Props para lista compartilhada
  sharedInfluencerIds = [],
  isSharedList = false
}: InfluencerGridProps) {
  const { t } = useTranslations();

  // 🔗 DEBUG: Log para verificar se os IDs estão chegando
  console.log('🔗 [INFLUENCER_GRID] Props recebidas:', {
    isSharedList,
    sharedInfluencerIds,
    count: sharedInfluencerIds?.length || 0
  });
  // ===== AUTENTICAÇÃO =====
  const { currentUser, getToken } = useAuth();
  const { user } = useUser();
  const { showLoader, hideLoader, updateMessage } = useGlobalLoader();
  
  // Estado para as marcas
  const [brands, setBrands] = useState<Brand[]>([]);
  const [selectedBrands, setSelectedBrands] = useState<string[]>(externalSelectedBrands);
  
  // Estado para categorias (necessário para o DataTable)
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState<boolean>(true);
  
  // Estado para o modo de visualização (grade ou lista)
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  
  // Estados para controle dos modais e diálogos
  const { toast: uiToast } = useToast();
  const [addInfluencerModalOpen, setAddInfluencerModalOpen] = useState(false);
  const [editInfluencerModalOpen, setEditInfluencerModalOpen] = useState(false);
  const [selectedInfluencers, setSelectedInfluencers] = useState<string[]>([]);
  const [selectionMode, setSelectionMode] = useState(false);
  // Estado do modal removido
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [influencerToDelete, setInfluencerToDelete] = useState<Influencer | null>(null);
  const [bulkDeleteDialogOpen, setBulkDeleteDialogOpen] = useState(false);
  // Estado do influenciador de detalhe removido
  
  // Estados para filtros locais
  const [searchTerm, setSearchQuery] = useState(externalSearchTerm || '');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedLocation, setSelectedLocation] = useState(externalLocation || '');
  const [minFollowers, setMinFollowers] = useState(externalMinFollowers || 0);
  const [maxFollowers, setMaxFollowers] = useState(externalMaxFollowers || 0);
  const [minRating, setMinRating] = useState(externalMinRating || 0);
  const [verifiedOnly, setVerifiedOnly] = useState(externalVerifiedOnly || false);
  const [availableOnly, setAvailableOnly] = useState(externalAvailableOnly || false);
  
  // Estados para controle de proposta selecionada
  const [proposalInfluencerIds, setProposalInfluencerIds] = useState<string[]>([]);
  const [snapshotInfluencers, setSnapshotInfluencers] = useState<any[]>([]);
  const [usingSnapshots, setUsingSnapshots] = useState(false); // 📸 APENAS para snapshots históricos reais
  const [usingPreFilteredData, setUsingPreFilteredData] = useState(false); // 🎯 Para dados pré-filtrados (cache ou snapshots)
  const [isLoadingSnapshots, setIsLoadingSnapshots] = useState(false);
  
  // ===== GRAPHQL HOOK =====
  // 🔥 IMPLEMENTAÇÃO CORRETA: Usar o userId do usuário autenticado
  const userId = currentUser?.id || 'system'; // Fallback para 'system' se não autenticado
 
  const {
    influencers,
    totalCount,
    loading,
    error: graphqlError,
    createInfluencer,
    updateInfluencer,
    deleteInfluencer,
    refreshData,
    loadMore,
    fetchInfluencerById
  } = useInfluencersGraphQL({
    userId,
    autoFetch: !selectedProposal, // 🔥 NOVA LÓGICA: Só fazer autoFetch quando NÃO há proposta selecionada
    // 🔥 CORREÇÃO: Usar filtros vazios para permitir cache completo
    filters: {},
    pagination: {
      limit: 50, // Aumentar limite para ter mais dados em cache
      offset: 0
    },
    infiniteScroll: true // Habilitar scroll infinito
  });

  // 🔥 FALLBACKS para propriedades que podem não existir no hook
  const loadingMore = false; // Simplificado  
  const hasNextPage = totalCount > influencers.length; // Calculado baseado no total vs carregados
  const selectedInfluencer = externalSelectedInfluencer; // Usar props externa
  const setSelectedInfluencer = (influencer: Influencer | null) => {
    // Usar callback se fornecido
    if (onSelectInfluencer && influencer) {
      onSelectInfluencer(influencer);
    }
  };


  // Conversão de erro GraphQL para string
  const error = graphqlError?.message || null;
  
  // ===== BUSCAR CATEGORIAS =====
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        // Tenta obter categorias do cache
        const cachedCategories = localStorage.getItem('categories');
        if (cachedCategories) {
          setCategories(JSON.parse(cachedCategories));
          setIsLoadingCategories(false);
          return;
        }

        // Se não houver cache, busca da API pública (para páginas compartilhadas)
        const response = await axios.get('/api/categories/public');
        setCategories(response.data);
        
        // Salva no cache
        localStorage.setItem('categories', JSON.stringify(response.data));
      } catch (error) {
      } finally {
        setIsLoadingCategories(false);
      }
    };

    fetchCategories();
  }, []);

  // 🔄 LISTENER PARA EVENTOS DE ATUALIZAÇÃO DE INFLUENCIADORES - SIMPLIFICADO
  useEffect(() => {
    const handleInfluencerUpdate = (event: CustomEvent) => {
      const { influencerId, action, data } = event.detail;
      console.log('📡 [GRID] Recebido evento influencer-updated:', { influencerId, action });
      
      if (action === 'create') {
        // ✅ MANTER: Para criação, fazer refresh para buscar novo influenciador
        console.log('🔄 [GRID] Novo influenciador criado, fazendo refresh para incluí-lo');
        setTimeout(() => {
          refreshData();
        }, 100);
      } else if (action === 'delete') {
        // ✅ MANTER: Para exclusão, fazer refresh para removê-lo
        console.log('🔄 [GRID] Influenciador deletado, fazendo refresh para removê-lo');
        setTimeout(() => {
          refreshData();
        }, 100);
      }
      // ✅ CORREÇÃO: Eventos de 'edit' não fazem mais refresh
      // O Apollo Cache e useInfluencersGraphQL lidam com isso automaticamente
    };

    // Adicionar listener para eventos custom
    window.addEventListener('influencer-updated', handleInfluencerUpdate as EventListener);
    
    // Cleanup: remover listener quando componente for desmontado
    return () => {
      window.removeEventListener('influencer-updated', handleInfluencerUpdate as EventListener);
    };
  }, [refreshData]);

  // Função para obter o nome da categoria a partir do ID
  const getCategoryName = (categoryId: string) => {
    if (!categoryId) return 'Sem categoria';
    
    // Se as categorias ainda estão carregando, mostra um placeholder
    if (isLoadingCategories) {
      return 'Carregando...';
    }
    
    // Buscar a categoria no array de categorias
    const category = categories.find(cat => cat.id === categoryId);
    
    if (category) {
      return category.name; // Retorna o nome real da categoria
    }
    
    // Se não encontrou a categoria, retorna o ID formatado como fallback
    return categoryId.charAt(0).toUpperCase() + categoryId.slice(1).toLowerCase();
  };
  
  // Função para obter a cor da categoria
  const getCategoryColor = (categoryId: string) => {
    if (!categoryId) return '#cccccc';
    
    // Buscar a categoria no array de categorias
    const category = categories.find(cat => cat.id === categoryId);
    
    if (category && category.color) {
      return category.color; // Retorna a cor definida da categoria
    }
    
    return "#9810fa"; // Cor padrão
  };

  // ===== COLUNAS DO DATA TABLE =====
  const columns: ColumnDef<Influencer>[] = [
    {
      id: "avatar",
      header: "",
      cell: ({ row }) => {
        const influencer = row.original;
        return (
          <div className="relative">
            <ZeroLCPInfluencerAvatar
              influencerName={influencer.name || "Sem nome"}
              avatarUrl={influencer.avatar}
              className="h-10 w-10"
              size="lg"
            />
            {influencer.verified && (
              <div className="absolute -bottom-0.5 -right-[0px] bg-transparent text-white rounded-full h-4 w-4 flex items-center justify-center">
                <svg id="Camada_2" data-name="Camada 2" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" viewBox="0 0 156.61 189.98" width="20" height="20">
                  <defs>
                    <linearGradient id="Gradiente_sem_nome_147" data-name="Gradiente sem nome 147" x1="52.12" y1="184.26" x2="115.13" y2="9.61" gradientUnits="userSpaceOnUse">
                      <stop offset="0" stopColor="#ff0074"/>
                      <stop offset="1" stopColor="#ff0074"/>
                    </linearGradient>
                  </defs>
                  <g id="Camada_1-2" data-name="Camada 1">
                    <path className="cls-1" fill="url(#Gradiente_sem_nome_147)" d="m155.35,97.66h0c-.82-4.58-2.05-9.01-3.63-13.28-5.77-15.52-16.32-28.72-29.87-37.79,1.72,4.96,2.66,10.29,2.66,15.84,0,4.5-.61,8.86-1.77,12.99-12.01-8.56-19.95-22.48-20.31-38.27-.01-.39-.02-.78-.02-1.16,0-4.5.61-8.86,1.77-12.99,1.02-3.68,2.46-7.18,4.28-10.44,2.62-4.73,6.01-8.97,10-12.56-9.86.78-19.21,3.38-27.71,7.47-3.97,1.91-7.75,4.15-11.32,6.68-8.21,5.82-15.25,13.19-20.7,21.68-7.82,12.18-12.35,26.68-12.35,42.22,0,3.88.28,7.68.83,11.41.71,4.87,1.87,9.59,3.43,14.12-3.55-2.2-6.8-4.85-9.67-7.87-8.22-8.67-13.26-20.39-13.26-33.29,0-4.04.5-7.96,1.43-11.7C14.54,62.52,4.26,79.45,1.06,98.78c-.7,4.2-1.06,8.51-1.06,12.9,0,43.25,35.06,78.3,78.3,78.3,27.69,0,52.03-14.38,65.95-36.08,7.82-12.19,12.35-26.68,12.35-42.23,0-4.78-.43-9.47-1.25-14.02Zm-77.15,77.38c-17.82,0-33.39-9.63-41.79-23.98,12.07,7.61,26.37,12.01,41.69,12.01,12.16,0,23.68-2.77,33.94-7.72,2.79-1.35,5.48-2.85,8.07-4.49-1.03,1.78-2.17,3.48-3.41,5.11-8.84,11.59-22.79,19.07-38.5,19.07Z"/>
                  </g>
                </svg>
              </div>
            )}
          </div>
        );
      },
      enableSorting: false,
      size: 50,
    },
    {
      id: "name",
      accessorKey: "name",
      header: t('influencers.table_headers.name') as string,
      cell: ({ row }) => {
        const influencer = row.original;
        return (
          <div className="flex flex-col">
            <span className="font-medium">{influencer.name}</span>
          </div>
        );
      },
    },
    {
      id: "category",
      accessorKey: "category",
      header: t('influencers.table_headers.category') as string,
      cell: ({ row }) => {
        const influencer = row.original;
        return (
          <Badge 
            variant="secondary" 
            style={{ backgroundColor: getCategoryColor(influencer.category) + '20', color: getCategoryColor(influencer.category) }}
          >
            {getCategoryName(influencer.category)}
          </Badge>
        );
      },
    },
    {
      id: "location",
      header: t('influencers.table_headers.location') as string,
      cell: ({ row }) => {
        const influencer = row.original;
        return (
          <span className="text-sm text-muted-foreground">
            {formatLocationDisplay(influencer)}
          </span>
        );
      },
    },
    {
      id: "instagram",
      header: t('influencers.table_headers.instagram'),
      cell: ({ row }) => {
        const influencer = row.original;
        const followers = influencer.socialNetworks?.instagram?.followers || 0;
        return (
          <div className="flex items-center gap-1">
            <SocialIcon platform="instagram" size={16} className="text-black dark:text-white" />
            <span className="text-sm">{formatarNumero(followers)}</span>
          </div>
        );
      },
    },
    {
      id: "youtube",
      header: t('influencers.table_headers.youtube'),
      cell: ({ row }) => {
        const influencer = row.original;
        const followers = influencer.socialNetworks?.youtube?.followers || 0;
        return (
          <div className="flex items-center gap-1">
            <SocialIcon platform="youtube" size={16} className="text-black dark:text-white" />
            <span className="text-sm">{formatarNumero(followers)}</span>
          </div>
        );
      },
    },
    {
      id: "tiktok",
      header: t('influencers.table_headers.tiktok'),
      cell: ({ row }) => {
        const influencer = row.original;
        const followers = influencer.socialNetworks?.tiktok?.followers || 0;
        return (
          <div className="flex items-center gap-1">
            <SocialIcon platform="tiktok" size={16} className="text-black dark:text-white" />
            <span className="text-sm">{formatarNumero(followers)}</span>
          </div>
        );
      },
    },
  
  ];
  
  // ===== SCROLL INFINITO =====
  const { loadingRef } = useInfiniteScroll({
    hasMore: hasNextPage,
    isLoading: loadingMore || loading,
    onLoadMore: loadMore,
    threshold: 200 // Carregar quando estiver a 200px do final
  });
  
 

  
  // ===== FUNÇÃO DE PROCESSAMENTO GRAPHQL =====
  // Processa dados do GraphQL para garantir compatibilidade com o formato antigo
  
  // Função utilitária para formatar localização filtrando valores inválidos
  const formatLocationDisplayGraphQL = (city: string, state: string, country: string) => {
    const validCity = city && city !== "Não informado" && city.trim() !== "" ? city : null;
    const validState = state && state !== "Não informado" && state.trim() !== "" ? state : null;
    const validCountry = country && country !== "Não informado" && country.trim() !== "" ? country : null;
    
    if (validCity && validState) {
      return `${validCity}, ${validState}`;
    } else if (validCity && validCountry) {
      return `${validCity}, ${validCountry}`;
    } else if (validState && validCountry) {
      return `${validState}, ${validCountry}`;
    } else if (validCountry) {
      return validCountry;
    } else if (validCity) {
      return validCity;
    } else if (validState) {
      return validState;
    }
    return "";
  };
  
  const processGraphQLInfluencer = (inf: any) => ({
        ...inf,
    // Garantir que alguns campos essenciais existam para compatibilidade
        gradient: inf.gradient || gradients[Math.floor(Math.random() * gradients.length)],
        rating: parseFloat(inf.rating || '4.0'),
    mainCategories: Array.isArray(inf.categories) ? inf.categories : [],
    // Formatar campos numéricos como string para compatibilidade
    instagram: inf.instagramFollowers?.toString() || '0',
    youtube: inf.youtubeFollowers?.toString() || '0',
    tiktok: inf.tiktokFollowers?.toString() || '0',
        whatsapp: inf.whatsapp?.toString() || '',
    // 🔥 CORREÇÃO: Mapear campos do GraphQL para o formato esperado - manter isVerified do GraphQL
    verified: inf.isVerified || false,
    isVerified: inf.isVerified || false,
    location: formatLocationDisplayGraphQL(inf.city, inf.state, inf.country) || inf.location || '',
    totalFollowers: inf.totalFollowers?.toString() || '0',
    
    // 🔥 NOVA CORREÇÃO: Garantir que campos administrativos sejam sempre mapeados
    responsibleName: inf.responsibleName || null,
    agencyName: inf.agencyName || null,
    
    // 🔥 CORREÇÃO: Mapear estrutura socialNetworks para compatibilidade com tabela
    socialNetworks: {
      instagram: {
        username: inf.instagramUsername || '',
        followers: inf.instagramFollowers || 0
      },
      youtube: {
        username: inf.youtubeUsername || '',
        followers: inf.youtubeFollowers || 0
      },
      tiktok: {
        username: inf.tiktokUsername || '',
        followers: inf.tiktokFollowers || 0
      },
      facebook: {
        username: inf.facebookUsername || '',
        followers: inf.facebookFollowers || 0
      },
      twitch: {
        username: inf.twitchUsername || '',
        followers: inf.twitchFollowers || 0
      },
      kwai: {
        username: inf.kwaiUsername || '',
        followers: inf.kwaiFollowers || 0
      }
    }
  });
  
  // 🚀 FASE 6: Removido fetchBrands - agora usa hook useBrandsList()
  // As marcas são obtidas diretamente via hook no BrandFilter
  
  // Função para selecionar um influenciador
  const handleOpenInfluencerDetail = (influencer: Influencer) => {
    
    // 🔥 MARCAR que houve seleção manual para evitar conflito com seleção automática
    setHasInitialSelection(true);
    
    // 🔥 SEMPRE chamar a função externa, mesmo se for o mesmo influenciador, para garantir que a URL seja atualizada
    if (onSelectInfluencer) {
      // Debug: Verificar dados demográficos antes de passar para o painel
      console.log('🔍 [DEBUG SELECT] Influenciador selecionado:', influencer.name);
      console.log('🔍 [DEBUG SELECT] currentDemographics:', influencer.currentDemographics);
      console.log('🔍 [DEBUG SELECT] isArray:', Array.isArray(influencer.currentDemographics));
      console.log('🔍 [DEBUG SELECT] length:', influencer.currentDemographics?.length);

      onSelectInfluencer(influencer);
    }
    
    // Atualiza o estado local para destacar o card
    setSelectedInfluencer(influencer);
  };
  
  // Função para abrir o modal de edição (GraphQL)
  const handleEditInfluencer = async (influencer: Influencer) => {
      try {
      
      // Usar GraphQL para buscar dados completos
      const fullInfluencerData = await fetchInfluencerById(String(influencer.id));
        
      if (fullInfluencerData) {
      setSelectedInfluencer(fullInfluencerData);
      setEditInfluencerModalOpen(true);
      } else {
        throw new Error('Influenciador não encontrado');
      }
      
    } catch (error) {
      
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
        toast.error(`Erro ao carregar dados do influenciador: ${errorMessage}`);
    }
  };
  
  // Função para confirmar exclusão de um influenciador
  const handleDeleteConfirm = (influencer: Influencer) => {
    setInfluencerToDelete(influencer);
    setDeleteDialogOpen(true);
  };

  // Função para enviar influenciadores selecionados para uma marca
  const handleSendToBrand = async (brandId: string, brandName: string) => {
    if (selectedInfluencers.length === 0) {
      toast.error('Nenhum influenciador selecionado para enviar.');
      return;
    }

    try {
      console.log('🚀 Enviando influenciadores para a marca:', {
        brandId,
        brandName,
        influencerIds: selectedInfluencers,
        count: selectedInfluencers.length
      });

      // Verificar autenticação usando Clerk
      if (!user) {
        toast.error('Usuário não autenticado. Faça login novamente.');
        return;
      }

      const token = await getToken();

      // Enviar apenas os IDs dos influenciadores para a API
      const response = await fetch('/api/campanhas/influencers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`, // ✅ Incluir token de autenticação
        },
        body: JSON.stringify({
          brandId,
          brandName,
          influencerIds: selectedInfluencers, // Enviar apenas os IDs
          createdBy: user.id // ✅ Usar ID do usuário autenticado
        }),
      });

      if (!response.ok) {
        let errorMessage = `Erro HTTP ${response.status}: ${response.statusText}`;
        let errorData = null;
        
        try {
          const responseText = await response.text();
          if (responseText) {
            try {
              errorData = JSON.parse(responseText);
              errorMessage = errorData.error || errorData.message || errorMessage;
            } catch (jsonError) {
              // Se não conseguir fazer parse do JSON, use o texto da resposta
              errorMessage = responseText || errorMessage;
            }
          }
        } catch (readError) {
        }
        
        throw new Error(errorMessage);
      }

      const result = await response.json();
      
      // Limpar seleção após envio bem-sucedido
      setSelectedInfluencers([]);
      setSelectionMode(false);
      
      // Mostrar feedback de sucesso
      toast.success(result.message || `${selectedInfluencers.length} influenciador(es) enviado(s) com sucesso para ${brandName}!`);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido ao enviar influenciadores para a marca.';
      toast.error(errorMessage);
    }
  };

  // Função para duplicar um influenciador (GraphQL)
  const handleDuplicateInfluencer = async (influencerData: any) => {
    try {
      
      // Preparar dados para duplicação
      const duplicateData = {
        name: `${influencerData.name} (Cópia)`,
        email: influencerData.email,
        country: influencerData.country || "Brasil",
        state: influencerData.state || "",
        city: influencerData.city || "",
        gender: influencerData.gender || "Não informado",
        category: influencerData.category || null,
        categories: influencerData.categories || [],
      };
      
      // Usar GraphQL para criar o influenciador duplicado
      await createInfluencer(duplicateData);
      
    } catch (error) {
      toast.error('Erro ao duplicar influenciador. Tente novamente.');
    }
  };
  
  // 🔍 INVESTIGAÇÃO: Monitorar mudanças nos influenciadores
  useEffect(() => {
    // Log removido para limpar console
  }, [influencers, loading, error, userId]);

  // Controlar loader global baseado no estado de loading dos influencers + snapshots
  useEffect(() => {
    if (loading || isLoadingSnapshots) {
      if (isLoadingSnapshots) {
        updateMessage('Carregando dados históricos...');
      }
      showLoader();
    } else {
      hideLoader();
    }
  }, [loading, isLoadingSnapshots, showLoader, hideLoader, updateMessage]);

  // 🔥 CORREÇÃO: Efeito para refresh usando GraphQL
  useEffect(() => {
    // Só executa quando onRefresh for definido
    if (onRefresh !== undefined && onRefresh > 0) {
      refreshData();
    }
  }, [onRefresh, refreshData]);
  
  // Estado para controlar se já foi feita a seleção inicial
  const [hasInitialSelection, setHasInitialSelection] = useState(false);
  
  // 🔥 CORREÇÃO: Seleção automática simplificada - só executa uma vez
  useEffect(() => {
    // Só seleciona automaticamente se:
    // 🔥 DESABILITADO TEMPORARIAMENTE: Seleção automática do primeiro influencer
    // Esta lógica estava conflitando com a seleção manual do usuário
    // 1. Tem influencers carregados
    // 2. Não está carregando
    // 3. Tem função de seleção
    // 4. Ainda não fez seleção inicial
    // 5. Não tem influencer selecionado via URL
    // if (influencers.length > 0 && !loading && onSelectInfluencer && !hasInitialSelection && !selectedInfluencerId) {
    //   
    //   // Usar timeout para garantir que a seleção aconteça após o render
    //   setTimeout(() => {
    //     if (!selectedInfluencerId && !hasInitialSelection) { // Verificar novamente para evitar race conditions
    //       onSelectInfluencer(influencers[0]);
    //       setHasInitialSelection(true);
    //     }
    //   }, 100);
    // } else {
    //   // Log removido para limpar console
    // }
  }, [influencers.length, loading]); // 🔥 SIMPLIFICADO - só reage a mudanças essenciais
  
  // Reset da seleção inicial quando há refresh
  useEffect(() => {
    if (onRefresh !== undefined && onRefresh > 0) {
      setHasInitialSelection(false);
    }
  }, [onRefresh]);

  // 🔥 NOVO: Seleção automática do influenciador baseado na URL
  useEffect(() => {
    // 🔥 CORREÇÃO: Usar a mesma lógica de sourceInfluencers para buscar o influenciador
    const currentSourceInfluencers = selectedProposal && usingPreFilteredData ? snapshotInfluencers : influencers;

    // 🔥 CORREÇÃO: Só executar se realmente precisa mudar e reduzir logs
    // DESABILITADO TEMPORARIAMENTE para evitar loops de seleção
    // if (selectedInfluencerId &&
    //     currentSourceInfluencers.length > 0 &&
    //     !loading &&
    //     onSelectInfluencer &&
    //     selectedInfluencer?.id !== selectedInfluencerId) {

    //   const foundInfluencer = currentSourceInfluencers.find((inf: any) => inf.id === selectedInfluencerId);

    //   if (foundInfluencer) {
    //     console.log('👤 [URL] Selecionando influenciador da URL:', foundInfluencer.name, 'ID:', foundInfluencer.id);
    //     setSelectedInfluencer(foundInfluencer);
    //     onSelectInfluencer(foundInfluencer);
    //     setHasInitialSelection(true); // Prevenir seleção automática do primeiro
    //   }
    // }
  }, [selectedInfluencerId, influencers.length, snapshotInfluencers.length, loading, selectedProposal?.id, usingPreFilteredData, selectedInfluencer?.id]); // 🔥 OTIMIZADO: inclui selectedInfluencer?.id para evitar execuções desnecessárias

  // 🔥 NOVO: Função para carregar snapshots de uma proposta
  const loadProposalSnapshots = async (proposalId: string) => {
    if (!currentUser) return;
    
    setIsLoadingSnapshots(true);
    setUsingSnapshots(false);
    setUsingPreFilteredData(false);
    
    try {
      
      if (!currentUser?.id) {
        // Não é erro fatal, apenas pular snapshots e usar dados atuais via GraphQL
        return;
      }

      // Obter token do Clerk Auth se disponível
      let token = '';
      try {
        token = await getToken() || '';
      } catch (tokenError) {
        // Continuar sem token - algumas APIs podem não precisar
      }
      
      const response = await fetch(`/api/snapshots/proposal/${proposalId}?userId=${currentUser.id}`, {
        headers: {
          ...(token && { 'Authorization': `Bearer ${token}` }),
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        // Se a API não suporta snapshots ou retorna erro, usar dados atuais via GraphQL
        setUsingSnapshots(false);
        setUsingPreFilteredData(false);
        return;
      }

      const result = await response.json();
      
      if (result.success && result.data.totalCount > 0) {
        
        // Processar snapshots para o formato compatível com o grid
        const processedSnapshots = result.data.influencers.map((snapshot: any) => ({
          ...snapshot,
          // Manter compatibilidade com estrutura atual
          id: snapshot.originalInfluencerId || snapshot.id,
          // Adicionar flag para indicar que é snapshot
          isSnapshot: true,
          snapshotCapturedAt: snapshot.capturedAt,
          snapshotVersion: snapshot.version,
          // Processar dados das redes sociais para compatibilidade
          instagram: snapshot.socialNetworks?.instagram?.followers?.toString() || '0',
          totalFollowers: snapshot.totalFollowers?.toString() || '0',
          location: snapshot.location || '',
          verified: snapshot.isVerified || false,
          isVerified: snapshot.isVerified || false,
          gradient: snapshot.gradient || gradients[Math.floor(Math.random() * gradients.length)],
          // 🔥 CORREÇÃO: Adicionar estrutura socialNetworks para snapshots
          socialNetworks: {
            instagram: {
              username: snapshot.instagramUsername || '',
              followers: snapshot.instagramFollowers || 0
            },
            youtube: {
              username: snapshot.youtubeUsername || '',
              followers: snapshot.youtubeFollowers || 0
            },
            tiktok: {
              username: snapshot.tiktokUsername || '',
              followers: snapshot.tiktokFollowers || 0
            },
            facebook: {
              username: snapshot.facebookUsername || '',
              followers: snapshot.facebookFollowers || 0
            },
            twitch: {
              username: snapshot.twitchUsername || '',
              followers: snapshot.twitchFollowers || 0
            },
            kwai: {
              username: snapshot.kwaiUsername || '',
              followers: snapshot.kwaiFollowers || 0
            }
          }
        }));
        
        setSnapshotInfluencers(processedSnapshots);
        setUsingSnapshots(true); // ✅ AGORA SIM são snapshots históricos reais
        setUsingPreFilteredData(true); // ✅ E também são dados pré-filtrados
        setProposalInfluencerIds(processedSnapshots.map((s: any) => s.id));
        
        
      } else {
        setSnapshotInfluencers([]);
        setUsingSnapshots(false);
        setUsingPreFilteredData(false);
        // Continuar com carregamento normal de influencers da proposta via GraphQL
      }
      
    } catch (error) {
      setSnapshotInfluencers([]);
      setUsingSnapshots(false);
      setUsingPreFilteredData(false);
      // Fallback para dados atuais via GraphQL
    } finally {
      setIsLoadingSnapshots(false);
    }
  };

  // 🔥 NOVA IMPLEMENTAÇÃO: Hook GraphQL para buscar influencers da proposta - OTIMIZADO
  const {
    data: proposalInfluencersData,
    loading: loadingProposalInfluencers,
    error: proposalInfluencersError,
    refetch: refetchProposalInfluencers
  } = useQuery(GET_PROPOSAL_INFLUENCERS, {
    variables: { 
      influencerIds: proposalInfluencerIds,
      userId: userId,
      proposalId: selectedProposal?.id
    },
    skip: !selectedProposal || proposalInfluencerIds.length === 0 || !userId,
    fetchPolicy: 'cache-first', // 🔥 OTIMIZAÇÃO: Priorizar cache
    errorPolicy: 'all',
    notifyOnNetworkStatusChange: false, // 🔥 OTIMIZAÇÃO: Reduzir re-renders
    onCompleted: (data) => {
      if (data?.influencersByIds?.influencers) {
        // Logs removidos para limpar console
        setSnapshotInfluencers(data.influencersByIds.influencers);
        setUsingPreFilteredData(true);
        
        if (data.influencersByIds.hasPartialFailure) {
          // Log removido para limpar console
        }
      }
    },
    onError: (error) => {
      setUsingPreFilteredData(false);
    }
  });

  // 🚀 NOVA IMPLEMENTAÇÃO: Query GraphQL dedicada para buscar influencers de proposta
  const GET_PROPOSAL_INFLUENCERS_DIRECT = gql`
    query GetProposalInfluencersDirect($proposalId: ID!, $userId: ID!) {
      proposalInfluencers(proposalId: $proposalId, userId: $userId) {
        influencerIds
        totalCount
        success
      }
    }
  `;

  // Hook GraphQL para buscar IDs dos influencers da proposta
  const {
    data: proposalInfluencerIdsData,
    loading: loadingProposalIds,
    error: proposalIdsError,
    refetch: refetchProposalIds
  } = useQuery(GET_PROPOSAL_INFLUENCERS_DIRECT, {
    variables: {
      proposalId: selectedProposal?.id,
      userId: userId
    },
    skip: !selectedProposal?.id || !userId,
    fetchPolicy: 'cache-first',
    errorPolicy: 'all',
    onCompleted: (data) => {
      if (data?.proposalInfluencers?.success) {
        const influencerIds = data.proposalInfluencers.influencerIds || [];
        // Log removido para limpar console
        setProposalInfluencerIds(influencerIds);
        }
    },
    onError: (error) => {
        setProposalInfluencerIds([]);
      }
  });

  // 🔗 NOVO: Hook GraphQL para buscar influenciadores de lista compartilhada
  const [sharedInfluencers, setSharedInfluencers] = useState<any[]>([]);
  const [usingSharedData, setUsingSharedData] = useState(false);

  const {
    data: sharedInfluencersData,
    loading: loadingSharedInfluencers,
    error: sharedInfluencersError,
    refetch: refetchSharedInfluencers
  } = useQuery(GET_PROPOSAL_INFLUENCERS, {
    variables: {
      influencerIds: sharedInfluencerIds,
      userId: userId || currentUser?.id || 'shared', // Usar userId atual se disponível
      proposalId: 'shared_list' // Usar identificador especial para listas compartilhadas
    },
    skip: !isSharedList || !sharedInfluencerIds || sharedInfluencerIds.length === 0,
    fetchPolicy: 'cache-first',
    errorPolicy: 'all',
    notifyOnNetworkStatusChange: false,
    onCompleted: (data) => {
      if (data?.influencersByIds?.influencers) {
        console.log('🔗 [SHARED_LIST] Influenciadores carregados:', {
          total: data.influencersByIds.influencers.length,
          requested: sharedInfluencerIds.length
        });

        setSharedInfluencers(data.influencersByIds.influencers);
        setUsingSharedData(true);

        // Debug: Verificar se os dados demográficos estão presentes
        console.log('🔍 [DEBUG SHARED] Primeiro influenciador:', data.influencersByIds.influencers[0]);
        console.log('🔍 [DEBUG SHARED] currentDemographics:', data.influencersByIds.influencers[0]?.currentDemographics);

        if (data.influencersByIds.hasPartialFailure) {
          console.warn('🔗 [SHARED_LIST] Alguns influenciadores não foram encontrados');
        }
      }
    },
    onError: (error) => {
      console.error('🔗 [SHARED_LIST] Erro ao carregar influenciadores:', error);
      setUsingSharedData(false);
      setSharedInfluencers([]);
    }
  });

  // Effect principal para resetar estado quando proposta é removida
  useEffect(() => {
    if (!selectedProposal?.id) {
      setProposalInfluencerIds([]);
      setSnapshotInfluencers([]);
      setUsingSnapshots(false);
      setUsingPreFilteredData(false);
    } else {
    }
  }, [selectedProposal?.id]);
  
  // 🔥 CORREÇÃO: Props externas - usar useMemo para estabilizar dependências
  const externalPropsKey = useMemo(() => {
    return JSON.stringify({
      searchTerm: externalSearchTerm,
      location: externalLocation,
      minFollowers: externalMinFollowers,
      maxFollowers: externalMaxFollowers,
      minRating: externalMinRating,
      verifiedOnly: externalVerifiedOnly,
      availableOnly: externalAvailableOnly,
      selectedBrands: externalSelectedBrands
    });
  }, [externalSearchTerm, externalLocation, externalMinFollowers, externalMaxFollowers, externalMinRating, externalVerifiedOnly, externalAvailableOnly, externalSelectedBrands]);

  useEffect(() => {
    let shouldUpdate = false;
    const updates: any = {};
    
    if (externalSearchTerm !== undefined && externalSearchTerm !== searchTerm) {
      updates.searchTerm = externalSearchTerm;
      shouldUpdate = true;
    }
    if (externalLocation !== undefined && externalLocation !== selectedLocation) {
      updates.selectedLocation = externalLocation;
      shouldUpdate = true;
    }
    if (externalMinFollowers !== undefined && externalMinFollowers !== minFollowers) {
      updates.minFollowers = externalMinFollowers;
      shouldUpdate = true;
    }
    if (externalMaxFollowers !== undefined && externalMaxFollowers !== maxFollowers) {
      updates.maxFollowers = externalMaxFollowers;
      shouldUpdate = true;
    }
    if (externalMinRating !== undefined && externalMinRating !== minRating) {
      updates.minRating = externalMinRating;
      shouldUpdate = true;
    }
    if (externalVerifiedOnly !== undefined && externalVerifiedOnly !== verifiedOnly) {
      updates.verifiedOnly = externalVerifiedOnly;
      shouldUpdate = true;
    }
    if (externalAvailableOnly !== undefined && externalAvailableOnly !== availableOnly) {
      updates.availableOnly = externalAvailableOnly;
      shouldUpdate = true;
    }
    if (externalSelectedBrands !== undefined && JSON.stringify(externalSelectedBrands) !== JSON.stringify(selectedBrands)) {
      updates.selectedBrands = externalSelectedBrands;
      shouldUpdate = true;
    }
    
    if (shouldUpdate) {
      // Atualizar todos os estados de uma vez para evitar múltiplas re-renderizações
      if (updates.searchTerm !== undefined) setSearchQuery(updates.searchTerm);
      if (updates.selectedLocation !== undefined) setSelectedLocation(updates.selectedLocation);
      if (updates.minFollowers !== undefined) setMinFollowers(updates.minFollowers);
      if (updates.maxFollowers !== undefined) setMaxFollowers(updates.maxFollowers);
      if (updates.minRating !== undefined) setMinRating(updates.minRating);
      if (updates.verifiedOnly !== undefined) setVerifiedOnly(updates.verifiedOnly);
      if (updates.availableOnly !== undefined) setAvailableOnly(updates.availableOnly);
      if (updates.selectedBrands !== undefined) setSelectedBrands(updates.selectedBrands);
    }
  }, [externalPropsKey]);
  
  // 🔥 NOVO: Decidir qual fonte de dados usar baseado no contexto
  let sourceInfluencers;

  if (isSharedList && usingSharedData) {
    // 🔗 Lista compartilhada: usar dados dos influenciadores compartilhados
    sourceInfluencers = sharedInfluencers;
    console.log('🔗 [SHARED_LIST] Usando dados compartilhados:', sharedInfluencers.length);
  } else if (selectedProposal && usingPreFilteredData) {
    // 📋 Proposta selecionada: usar dados pré-filtrados
    sourceInfluencers = snapshotInfluencers;
  } else {
    // 🌐 Visualização geral: usar todos os influenciadores
    sourceInfluencers = influencers;
  }

  // 🔥 CORREÇÃO: Processar dados do GraphQL para garantir compatibilidade
  const processedInfluencers = sourceInfluencers.map(processGraphQLInfluencer);
  
  // 🔧 CORREÇÃO: Garantir que processedInfluencers é sempre um array
  const safeSourceInfluencers = Array.isArray(processedInfluencers) ? processedInfluencers : [];
  
  // 🔧 CORREÇÃO: Eliminar duplicações antes da filtragem
  const uniqueInfluencers = safeSourceInfluencers.reduce((acc: any[], current: any) => {
    const existingInfluencer = acc.find((inf: any) => inf.id === current.id);
    if (!existingInfluencer) {
      acc.push(current);
    }
    return acc;
  }, [] as any[]);

  // Log para verificar se há duplicações
  if (safeSourceInfluencers.length !== uniqueInfluencers.length) {
  }
  
  // Log para debug do tipo de dados recebidos
  if (!Array.isArray(sourceInfluencers)) {
  }

  

  // Filtragem de influenciadores baseada nos filtros
  const filteredInfluencers = uniqueInfluencers.filter((influencer: any) => {
    // 🔗 FILTRO PARA LISTA COMPARTILHADA: Se estamos usando dados compartilhados, pular filtros de proposta
    if (isSharedList && usingSharedData) {
      // Dados já estão filtrados pelos IDs compartilhados, aplicar apenas filtros básicos
      // Pular para filtros de localização, seguidores, etc.
    } else if (selectedProposal && usingPreFilteredData) {
      // 🔥 FILTRO POR PROPOSTA: Se estamos usando dados pré-filtrados, pular filtro de proposta
      // Logs removidos para limpar console
      // Dados já estão filtrados pela proposta, pular para outros filtros
    } else if (selectedProposal && !usingPreFilteredData && proposalInfluencerIds.length > 0) {
      // Se tem proposta mas não está usando dados pré-filtrados, aplicar filtro manual
      const isIncluded = proposalInfluencerIds.includes(influencer.id);
      if (!isIncluded) {
        // Log removido para limpar console
        return false;
      }
      // Log removido para limpar console
    } else if (selectedProposal && proposalInfluencerIds.length === 0) {
      // Proposta selecionada mas sem influencers ou ainda carregando
      return false; // Não mostrar nada até carregar
    }
    
    // Filtro de pesquisa
    if (searchTerm && !influencer.name.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }
    
    // Filtro de categoria
    if (selectedCategory !== 'all' && influencer.category !== selectedCategory) {
      return false;
    }
    
    // Filtro de localização
    if (selectedLocation && !influencer.location.includes(selectedLocation)) {
      return false;
    }
    
    // Filtro de seguidores
    const totalFollowers = parseInt(influencer.totalFollowers || '0', 10);
    if (minFollowers > 0 && totalFollowers < minFollowers) {
      return false;
    }
    if (maxFollowers > 0 && totalFollowers > maxFollowers) {
      return false;
    }
    
    // Filtro de avaliação mínima
    if (influencer.rating < minRating) {
      return false;
    }
    
    // 🔥 CORREÇÃO: Filtro de exclusivos (verificados) - usar isVerified do GraphQL
    if (verifiedOnly && !influencer.isVerified) {
      return false;
    }
    
    // Filtro de disponibilidade (simplificação: consideramos disponíveis os que têm rating acima de 4)
    if (availableOnly && influencer.rating < 4) {
      return false;
    }
    
    // Filtro de marcas
    if (selectedBrands.length > 0) {
      const influencerBrands = [
        ...(influencer.financialData?.brandHistory?.instagram || []),
        ...(influencer.financialData?.brandHistory?.tiktok || []),
        ...(influencer.financialData?.brandHistory?.youtube || [])
      ];
      
      return selectedBrands.some(brandId => 
        influencerBrands.includes(brandId)
      );
    }
    
    return true;
  });

  // 🔥 ANTIGO: Atualizar dados da proposta quando novos influencers chegam via GraphQL
  // REMOVIDO: Esta lógica foi substituída pela query GraphQL dedicada
  /*
  useEffect(() => {
    if (selectedProposal && proposalInfluencerIds.length > 0 && !usingPreFilteredData && influencers.length > 0) {
      // Verificar se agora temos todos os influencers necessários no cache
      const cachedInfluencers = influencers.filter((inf: any) => proposalInfluencerIds.includes(inf.id));
      
      if (cachedInfluencers.length === proposalInfluencerIds.length) {
        setSnapshotInfluencers(cachedInfluencers);
        setUsingSnapshots(false); // ❌ Não são snapshots históricos
        setUsingPreFilteredData(true); // ✅ Agora temos dados pré-filtrados
      }
    }
  }, [influencers.length, selectedProposal, proposalInfluencerIds, usingPreFilteredData]);
  */

  // 🔥 NOVO: Detectar quando a query GraphQL da proposta carrega dados
  useEffect(() => {
    if (proposalInfluencersData?.influencersByIds && selectedProposal) {
      const influencersArray = proposalInfluencersData.influencersByIds.influencers || [];

      // Garantir que sempre setamos um array
      if (Array.isArray(influencersArray)) {
        setSnapshotInfluencers(influencersArray);
        setUsingSnapshots(false); // ❌ Não são snapshots históricos
        setUsingPreFilteredData(true); // ✅ Dados pré-filtrados via GraphQL

        // 🔥 CORREÇÃO: Auto-selecionar primeiro influenciador apenas se:
        // 1. Não há influencer na URL
        // 2. Não foi feita seleção inicial ainda
        // 3. Não há influencer selecionado atualmente
        if (!selectedInfluencerId &&
            !hasInitialSelection &&
            !selectedInfluencer &&
            influencersArray.length > 0 &&
            onSelectInfluencer) {
          const firstInfluencer = influencersArray[0];
          console.log('🎯 [AUTO_SELECT] Selecionando primeiro influenciador da proposta:', firstInfluencer.name, 'ID:', firstInfluencer.id);
          setSelectedInfluencer(firstInfluencer);
          onSelectInfluencer(firstInfluencer);
          setHasInitialSelection(true);
        }
      } else {
        setSnapshotInfluencers([]);
        setUsingPreFilteredData(false);
      }
    }
  }, [proposalInfluencersData, selectedProposal, loadingProposalInfluencers]); // 🔥 REMOVIDO dependências que causam loop

  // Log removido para limpar console
  useEffect(() => {
    // Log removido para limpar console
  }, [selectedProposal, proposalInfluencerIds, filteredInfluencers.length, uniqueInfluencers.length, usingSnapshots, usingPreFilteredData]);
  
  return (
    <div className="w-full">
      {/* Header fixo com Estatísticas, Filtros e Toolbar */}
      <AnimatedStats 
        influencersCount={influencers.length} 
        userId={userId}
        searchTerm={searchTerm}
        selectedCategory={selectedCategory}
        onSearchChange={setSearchQuery}
        onCategoryChange={setSelectedCategory}
        viewMode={viewMode}
        selectionMode={selectionMode}
        selectedCount={selectedInfluencers.length}
        onViewModeChange={setViewMode}
        onAddInfluencer={() => setAddInfluencerModalOpen(true)}
        onToggleSelectionMode={() => {
          setSelectionMode(!selectionMode);
          if (selectionMode) setSelectedInfluencers([]);
        }}
        onDeleteSelected={() => openBulkDeleteDialog(selectedInfluencers, setBulkDeleteDialogOpen)}
        onDuplicateSelected={() => handleBulkDuplicate(selectedInfluencers, influencers, setSelectedInfluencers, () => refreshData(), gradients)}
        onBrandFilterChange={(brands: string[]) => {
          if (selectedBrands !== brands) {
            setSelectedBrands(brands);
          }
        }}
        verifiedOnly={verifiedOnly}
        onVerifiedOnlyChange={setVerifiedOnly}
        onSendToBrand={handleSendToBrand}
      />
      
      {/* Conteúdo com espaçamento para compensar headers fixos */}
      <div className="space-y-6 px-2 pt-2">
      
    
      
      {/* Exibição de Influenciadores (Grid ou Lista) */}
      {error ? (
        <div className="text-center py-8 text-red-500">{error}</div>
      ) : filteredInfluencers.length === 0 ? (
        <div className="text-center py-12 text-muted-foreground">
          {selectedProposal && proposalInfluencerIds.length === 0 && !loadingProposalInfluencers ? (
            <div className="space-y-2">
              <p>Esta proposta não possui influencers.</p>
              <p className="text-xs">Adicione influencers à proposta para vê-los aqui.</p>
            </div>
          ) : (
            "Nenhum influenciador encontrado."
          )}
        </div>
      ) : (
        <>
          {viewMode === 'grid' ? (
            <GridView
              influencers={filteredInfluencers}
              onInfluencerClick={handleOpenInfluencerDetail}
              selectedInfluencers={selectedInfluencers}
              selectedInfluencer={selectedInfluencer}
              selectionMode={selectionMode}
              onToggleSelection={(id) => toggleInfluencerSelection(id, selectedInfluencers, setSelectedInfluencers)}
              onDuplicate={handleDuplicateInfluencer}
              onEdit={handleEditInfluencer}
              onDelete={handleDeleteConfirm}
              selectedInfluencerId={selectedInfluencerId}
              selectedProposal={selectedProposal}
              userId={userId}
              // 🔗 NOVO: Passar prop para lista compartilhada
              isSharedList={isSharedList}
            />
          ) : (
            <div className="space-y-2">
              <DataTable
                columns={columns}
                data={filteredInfluencers}
                enableRowSelection={true}
                enableColumnVisibility={true}
                enablePagination={true}
                enableSorting={true}
                enableFiltering={true}
                enableColumnOrdering={true}
                pageSize={20}
                searchPlaceholder="Buscar influenciadores..."
                onRowClick={handleOpenInfluencerDetail}
                selectionMode={selectionMode}
                selectedRows={selectedInfluencers}
                onRowSelect={(id: string) => toggleInfluencerSelection(id, selectedInfluencers, setSelectedInfluencers)}
                className="[&_tbody_tr]:border-b-2"
              />
            </div>
          )}

          {/* Indicador de scroll infinito */}
          {(hasNextPage || loadingMore) && (
            <div ref={loadingRef} className="flex justify-center py-8">
              {loadingMore ? (
                <div className="flex items-center gap-2 text-muted-foreground">
                  <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full"></div>
                  <span>Carregando mais influenciadores...</span>
                </div>
              ) : (
                <div className="text-muted-foreground/60 text-sm">
                  Carregue mais influenciadores rolando para baixo
                </div>
              )}
            </div>
          )}

          {/* Indicador de fim dos dados */}
          {!hasNextPage && filteredInfluencers.length > 0 && (
            <div className="text-center py-8 text-muted-foreground/60 text-sm">
              {t('influencers.all_loaded', { count: totalCount })}
            </div>
          )}
        </>
      )}
      
      </div> {/* Fim do conteúdo com espaçamento */}
      
      {/* Modal de Detalhes removido */}
      
      {/* Modal para adicionar influenciador - Isolado do space-y-6 */}
      <div className="absolute">
        <AddInfluencerForm 
          open={addInfluencerModalOpen} 
          onOpenChange={setAddInfluencerModalOpen}
          mode="create"
                    // Removido onSubmit para evitar dupla criação - o AddInfluencerForm já tem sua própria lógica de criação
        />
      </div>
      
      {/* Modal para editar influenciador usando o mesmo formulário de adição - Isolado do space-y-6 */}
      {selectedInfluencer && (
        <div className="absolute">
          <AddInfluencerForm 
            open={editInfluencerModalOpen} 
            onOpenChange={setEditInfluencerModalOpen}
            initialData={selectedInfluencer}
            mode="edit"
            onSubmit={async (data) => {
              try {
                
                // Usar GraphQL para atualizar influenciador
                const result = await updateInfluencer(String(selectedInfluencer.id), data);
                
                // Após submeter o formulário com sucesso, fechar o modal
                setEditInfluencerModalOpen(false);
                
                // Retornar o resultado
                return result;
              } catch (error) {
                toast.error('Erro ao atualizar influenciador. Tente novamente.');
                throw error;
              }
            }} 
          />
        </div>
      )}
      
      {/* Diálogo de confirmação para exclusão */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmar exclusão</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir o influenciador "{influencerToDelete?.name}"? 
              Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction 
                          onClick={async () => {
              if (influencerToDelete) {
                try {
                  await deleteInfluencer(String(influencerToDelete.id));
                  setDeleteDialogOpen(false);
                  setInfluencerToDelete(null);
                } catch (error) {
                  toast.error('Erro ao deletar influenciador');
                }
              }
            }}
              className="bg-red-600 hover:bg-red-700"
            >
              Excluir
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      {/* Diálogo de confirmação para exclusão em massa */}
      <AlertDialog open={bulkDeleteDialogOpen} onOpenChange={setBulkDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmar exclusão em massa</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir {selectedInfluencers.length} influenciador(es)? 
              Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction 
                          onClick={async () => {
              try {
                
                // Deletar cada influenciador via GraphQL
                for (const id of selectedInfluencers) {
                  await deleteInfluencer(String(id));
                }
                
                setSelectedInfluencers([]);
                setBulkDeleteDialogOpen(false);
              } catch (error) {
                toast.error('Erro ao deletar influenciadores');
              }
            }}
              className="bg-red-600 hover:bg-red-700"
            >
              Excluir {selectedInfluencers.length} influenciador(es)
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}



