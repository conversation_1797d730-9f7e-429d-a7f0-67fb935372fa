'use client';

import { useState, useEffect } from 'react';
import { useQuery, gql } from '@apollo/client';

// Query GraphQL para buscar dados básicos do influenciador
const GET_INFLUENCER_AVATAR = gql`
  query GetInfluencerAvatar($id: ID!) {
    influencer(id: $id) {
      id
      avatar
      name
    }
  }
`;

/**
 * Hook otimizado para gerenciar avatares de influenciadores
 * Prioriza URLs diretas e depois busca no GraphQL se necessário
 */
export function useInfluencerAvatar(influencerId: string, avatarPath?: string) {
  const [avatarUrl, setAvatarUrl] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  // Query GraphQL para buscar dados do influenciador
  const { data: influencerData, loading: graphqlLoading, error: graphqlError } = useQuery(GET_INFLUENCER_AVATAR, {
    variables: { id: influencerId },
    skip: !influencerId, // Pular query se não há ID
    errorPolicy: 'ignore', // Ignorar erros e usar placeholder
    onCompleted: (data) => {
      // Avatar data received
    },
    onError: (error) => {
      // Avatar fetch error handled
    }
  });

  useEffect(() => {
    // Prioridade 1: Se já temos uma URL válida, usar diretamente
    if (avatarPath && (
      avatarPath.startsWith('https://firebasestorage.googleapis.com') ||
      avatarPath.startsWith('https://storage.googleapis.com') ||
      avatarPath.startsWith('https://') ||
      avatarPath.startsWith('data:image/') // Para preview local
    )) {
      setAvatarUrl(avatarPath);
      setError(null);
      return;
    }

    // Prioridade 2: Se não há influencerId, usar fallback CSS
    if (!influencerId) {
      setAvatarUrl('');
      setError(null);
      return;
    }

    // Prioridade 3: Usar dados do GraphQL
    if (influencerData?.influencer?.avatar) {
      const foundAvatar = influencerData.influencer.avatar;
      
      if (foundAvatar && (
        foundAvatar.startsWith('https://firebasestorage.googleapis.com') ||
        foundAvatar.startsWith('https://storage.googleapis.com') ||
        foundAvatar.startsWith('https://')
      )) {
        setAvatarUrl(foundAvatar);
        setError(null);
        return;
      }
    }

    // Fallback: usar fallback CSS quando não está carregando e não tem dados
    if (!graphqlLoading && !influencerData?.influencer?.avatar) {
      setAvatarUrl('');
      setError(null);
    }

  }, [influencerId, avatarPath, influencerData, graphqlLoading]);

  return {
    avatarUrl,
    isLoading: graphqlLoading,
    error: graphqlError ? 'Erro ao carregar avatar' : error,
    refresh: () => {
      setError(null);
      // O Apollo Client fará o refetch automaticamente
    }
  };
}

/**
 * Hook simplificado que retorna apenas a URL do avatar
 * Usa lógica otimizada para diferentes tipos de URL
 */
export function useInfluencerAvatarUrl(influencerId: string, avatarPath?: string): string {
  // Se já temos uma URL válida, retornar diretamente
  if (avatarPath && (
    avatarPath.startsWith('https://firebasestorage.googleapis.com') ||
    avatarPath.startsWith('https://storage.googleapis.com') ||
    avatarPath.startsWith('https://') ||
    avatarPath.startsWith('data:image/')
  )) {
    return avatarPath;
  }
  
  // Para outros casos, usar o hook completo
  const { avatarUrl } = useInfluencerAvatar(influencerId, avatarPath);
  return avatarUrl;
}

/**
 * Hook para upload de avatar de influenciador para Firebase Storage
 * Substitui o uso de base64 por upload direto para gs://deumatch-demo.firebasestorage.app
 */
export function useInfluencerAvatarUpload() {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [uploadedUrl, setUploadedUrl] = useState<string | null>(null);

  const uploadAvatar = async (file: File, influencerId: string): Promise<string> => {
    try {
      setIsUploading(true);
      setUploadError(null);
      setUploadedUrl(null);

      // Validar arquivo
      if (!file.type.startsWith('image/')) {
        throw new Error('O arquivo deve ser uma imagem');
      }

      if (file.size > 5 * 1024 * 1024) { // 5MB
        throw new Error('O arquivo deve ter no máximo 5MB');
      }

      // Criar FormData para envio
      const formData = new FormData();
      formData.append('avatar', file);
      formData.append('influencerId', influencerId);

      // Fazer upload via API
      const response = await fetch('/api/influencers/avatar', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao fazer upload');
      }

      const result = await response.json();
      
      setUploadedUrl(result.avatarUrl);
      return result.avatarUrl;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      setUploadError(errorMessage);
      throw error;
    } finally {
      setIsUploading(false);
    }
  };

  const clearUploadState = () => {
    setUploadError(null);
    setUploadedUrl(null);
  };

  return {
    uploadAvatar,
    isUploading,
    uploadError,
    uploadedUrl,
    clearUploadState
  };
}

/**
 * Hook para gerenciar avatar temporário (apenas preview)
 * Upload só acontece no submit do formulário
 */
export function useInfluencerAvatarPreview() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);

  const selectFile = (file: File) => {
    try {
      setIsProcessing(true);
      
      // Validar arquivo
      if (!file.type.startsWith('image/')) {
        throw new Error('O arquivo deve ser uma imagem');
      }

      if (file.size > 5 * 1024 * 1024) { // 5MB
        throw new Error('O arquivo deve ter no máximo 5MB');
      }

      // Gerar preview
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setPreviewUrl(result);
        setSelectedFile(file);
      };
      reader.readAsDataURL(file);

    } catch (error) {
      throw error;
    } finally {
      setIsProcessing(false);
    }
  };

  const clearSelection = () => {
    setSelectedFile(null);
    setPreviewUrl('');
  };

  const uploadSelectedFile = async (influencerId: string): Promise<string | null> => {
    if (!selectedFile) return null;

    try {
      // Criar FormData para envio
      const formData = new FormData();
      formData.append('avatar', selectedFile);
      formData.append('influencerId', influencerId);

      // Fazer upload via API
      const response = await fetch('/api/influencers/avatar', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao fazer upload');
      }

      const result = await response.json();
      
      // Limpar arquivo local após upload
      clearSelection();
      
      return result.avatarUrl;

    } catch (error) {
      throw error;
    }
  };

  return {
    selectedFile,
    previewUrl,
    isProcessing,
    selectFile,
    clearSelection,
    uploadSelectedFile
  };
}

